import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';

class ImageCompressionUtils {
  /// Compresses an image to the target file size in KB
  /// Returns a File with the compressed image
  static Future<File> compressImageToSize(
    ui.Image image, {
    required int maxSizeKB,
    String? customFileName,
  }) async {
    final tempDir = await getTemporaryDirectory();
    final fileName = customFileName ?? 
        'compressed_image_${DateTime.now().millisecondsSinceEpoch}.jpg';
    final tempFile = File('${tempDir.path}/$fileName');

    // Start with high quality and progressively reduce
    int quality = 95;
    ui.Image currentImage = image;
    Uint8List? compressedBytes;

    // First attempt: Try different quality levels
    for (int q = quality; q >= 20; q -= 15) {
      compressedBytes = await _imageToBytes(currentImage, quality: q);
      final sizeKB = compressedBytes.length / 1024;
      
      debugPrint('Compression attempt: Quality $q, Size: ${sizeKB.toStringAsFixed(1)} KB');
      
      if (sizeKB <= maxSizeKB) {
        await tempFile.writeAsBytes(compressedBytes);
        debugPrint('Compression successful at quality $q');
        return tempFile;
      }
    }

    // Second attempt: Scale down the image
    double scaleFactor = 0.9;
    while (scaleFactor > 0.1) {
      final newWidth = (image.width * scaleFactor).round().clamp(100, image.width);
      final newHeight = (image.height * scaleFactor).round().clamp(100, image.height);
      
      currentImage = await _resizeImage(image, newWidth, newHeight);
      compressedBytes = await _imageToBytes(currentImage, quality: 85);
      
      final sizeKB = compressedBytes.length / 1024;
      debugPrint('Scaling attempt: ${newWidth}x$newHeight, Size: ${sizeKB.toStringAsFixed(1)} KB');
      
      if (sizeKB <= maxSizeKB) {
        await tempFile.writeAsBytes(compressedBytes);
        debugPrint('Compression successful with scaling: ${newWidth}x$newHeight');
        return tempFile;
      }
      
      scaleFactor -= 0.1;
    }

    // Final attempt: Aggressive compression
    final finalWidth = (image.width * 0.5).round().clamp(50, image.width);
    final finalHeight = (image.height * 0.5).round().clamp(50, image.height);
    
    currentImage = await _resizeImage(image, finalWidth, finalHeight);
    compressedBytes = await _imageToBytes(currentImage, quality: 60);
    
    await tempFile.writeAsBytes(compressedBytes);
    
    final finalSizeKB = compressedBytes.length / 1024;
    debugPrint('Final compression: ${finalWidth}x$finalHeight, Size: ${finalSizeKB.toStringAsFixed(1)} KB');
    
    return tempFile;
  }

  /// Converts ui.Image to bytes with specified quality
  static Future<Uint8List> _imageToBytes(ui.Image image, {int quality = 95}) async {
    // For now, we'll use PNG format as Flutter doesn't have built-in JPEG encoding
    // In a real app, you might want to use a package like 'image' for better compression
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    if (byteData == null) {
      throw Exception('Failed to convert image to bytes');
    }
    return byteData.buffer.asUint8List();
  }

  /// Resizes an image to the specified dimensions
  static Future<ui.Image> _resizeImage(ui.Image originalImage, int width, int height) async {
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    
    // Use medium quality for better performance vs quality balance
    final paint = Paint()..filterQuality = FilterQuality.medium;
    
    canvas.drawImageRect(
      originalImage,
      Rect.fromLTWH(0, 0, originalImage.width.toDouble(), originalImage.height.toDouble()),
      Rect.fromLTWH(0, 0, width.toDouble(), height.toDouble()),
      paint,
    );
    
    final picture = recorder.endRecording();
    return await picture.toImage(width, height);
  }

  /// Validates if a file size is within the acceptable range
  static Future<bool> isFileSizeAcceptable(File file, int maxSizeKB) async {
    final sizeBytes = await file.length();
    final sizeKB = sizeBytes / 1024;
    return sizeKB <= maxSizeKB;
  }

  /// Gets the file size in KB
  static Future<double> getFileSizeKB(File file) async {
    final sizeBytes = await file.length();
    return sizeBytes / 1024;
  }

  /// Checks if an image file is too large for processing
  static Future<bool> isImageTooLargeForProcessing(File file, {int maxSizeMB = 10}) async {
    final sizeBytes = await file.length();
    final sizeMB = sizeBytes / (1024 * 1024);
    return sizeMB > maxSizeMB;
  }
}

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:mastercookai/core/data/models/category_response.dart';
import 'package:mastercookai/core/data/models/cuisines_response.dart';
import 'package:mastercookai/core/data/models/recipe_response.dart';
import 'package:mastercookai/core/data/models/recipe_detail_response.dart';
import 'package:mastercookai/core/helpers/app_constant.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import '../../../app/imports/packages_imports.dart';
import '../../presentation/recipe/mobile_ui/sub_views/add_subview/add_recipe_success_bottom_sheet.dart';
import '../data/models/cookbook.dart';
import '../data/models/create_recipe_response.dart';
import '../data/models/recipe_delete_response.dart';
import '../data/models/recipes.dart';
import '../data/request_query/create_recipe_request.dart';
import '../data/request_query/paginantion_request.dart';
import '../network/base_notifier.dart';
import '../network/network_utils.dart';
import 'cookbook_notifier.dart';

// Use dynamic to allow data to hold List<Recipe> or RecipeDetails
final recipeNotifierProvider =
    StateNotifierProvider<RecipeNotifier, AppState<List<Recipe>>>(
  (ref) => RecipeNotifier(ref),
);

class RecipeNotifier extends BaseNotifier<List<Recipe>> {
  RecipeNotifier(Ref ref) : super(ref, const AppState<List<Recipe>>());


  Future<void> fetchRecipes({
    required BuildContext context,
    required int cookbookId,
    int? cuisineId,
    int? categoryId,
    required int currentPage,
    String? search,
    String? cookbookName,
    bool reset = false,
    int pageSize = 10,
    String? sort,
  }) async {
    if (!reset &&
        (state.hasMore == false || state.status == AppStatus.loadingMore)) {
      return;
    }

    final pageToFetch = reset ? 1 : currentPage;

    callDataService(
      repo.getRecipes(
          cookbookId.toString(),
          PaginationQueryParam(
              pageNumber: pageToFetch,
              pageSize: pageSize,
              search: search ?? '',
              itemSort: sort,
              cuisineId: cuisineId,
              categoryId: categoryId)),
      onStart: () => state = state.copyWith(
        status: reset ? AppStatus.loading : AppStatus.loadingMore,
      ),
      onSuccess: (response) => _handleSuccessResponse(response, !reset),
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message ?? 'Failed to fetch recipes',
        );
        if (context.mounted) {
          Utils().showFlushbar(context,message:  state.errorMessage!,isError: true);
        }
        print('Recipe fetch error: $e');
      },
      onComplete: () => print('Recipe fetch completed'),
    );
  }

  void _handleSuccessResponse(RecipeResponse response, bool loadMore) {
    final result = response.recipes;
    state = state.copyWith(
      status: result.isEmpty && !loadMore ? AppStatus.empty : AppStatus.success,
      data: loadMore ? [...state.data ?? [], ...result] : result,
      hasMore: state.currentPage < response.totalPageCount,
      currentPage: loadMore ? state.currentPage + 1 : 1,
      totalItems: response.totalRecords,
      totalPageCount: response.totalPageCount,
      errorMessage: null,
    );
  }

  Future<bool> createRecipe({
    required BuildContext context,
    required CreateRecipeRequest request,
    required int cookbookId,
  }) async {
    bool result = false;
    try{
    showLoader();
      await callDataService(
        repo.createRecipe(request, cookbookId),
        onStart: () => state = state.copyWith(
          status: AppStatus.creating,
        ),
        onSuccess: (CreateRecipeResponse response) {

          if (response.success) {
            state = state.copyWith(
              status: AppStatus.createSuccess,
              errorMessage: null,
            );
            Navigator.pop(context);
            if (context.mounted) {
              Utils().showFlushbar(
                context,
                message: response.message.general!.first ?? 'Added successfully',
                isError: false,
              );
            }
            result = true;
          } else {
            state = state.copyWith(
              status: AppStatus.createError,
              errorMessage:
              response.message.error?.join(', ') ?? 'Failed to create recipe',
            );
            if (context.mounted) {
              Utils().showFlushbar(context,
                  message: state.errorMessage!, isError: true);
            }
            print('Recipe creation failed: ${response.message.error}');
            result = false;
          }
        },
        onError: (e) {

          state = state.copyWith(
            status: AppStatus.createError,
            errorMessage: e.message ?? 'Failed to create recipe',
          );
          if (context.mounted) {
            Utils().showFlushbar(context,
                message: state.errorMessage!, isError: true);
          }
          print('Recipe creation error: $e');
          result = false;
        },
        onComplete: () => print('Recipe creation completed'),
      );
    }catch(e){
      state = state.copyWith(
        status: AppStatus.error,
        errorMessage: 'Failed to create recipe',
      );
    }finally{
      hideLoader();
    }

    return result;
  }

  Future<bool> updateRecipe({
    required BuildContext context,
    required CreateRecipeRequest request,
    required int cookbookId,
    required int recipeId,
  }) async {
    showLoader();
    final completer = Completer<bool>();
    callDataService(
      repo.updateRecipe(request, cookbookId, recipeId),
      onStart: () => state = state.copyWith(
        status: AppStatus.loading,
      ),
      onSuccess: (CreateRecipeResponse response) {
        hideLoader();
        if (response.success) {
          state = state.copyWith(
            status: AppStatus.success,
            errorMessage: null,
          );

          if (context.mounted) {
            Utils().showFlushbar(
              context,
              message: response.message.general!.first ?? 'Added successfully',
              isError: false,
              onDismissed: () {
                updateData(context, cookbookId: cookbookId);
              },
            );
            Utils().showFlushbar(context,
                message:
                    response.message.general!.first ?? 'Added successfully',
                isError: false, onDismissed: () {
              updateData(context, cookbookId: cookbookId);
            });
          }
          completer.complete(true);
        } else {
          state = state.copyWith(
            status: AppStatus.error,
            errorMessage:
                response.message.error?.join(', ') ?? 'Failed to create recipe',
          );
          if (context.mounted) {
            Utils().showFlushbar(context,
                message: state.errorMessage!, isError: true);
          }
          print('Recipe creation failed: ${response.message.error}');
          completer.complete(false);
        }
      },
      onError: (e) {
        hideLoader();
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message ?? 'Failed to create recipe',
        );
        if (context.mounted) {
          Utils().showSnackBar(context, state.errorMessage!);
        }
        print('Recipe creation error: $e');
        completer.complete(false);
      },
      onComplete: () => print('Recipe creation completed'),
    );
    return completer.future;
  }

  Future<bool> deleteRecipe({
    required BuildContext context,
    required int cookbookId,
    required int recipeId,
  }) async {
    final bool? confirmed = await Utils().showCommonConfirmDialog(
      context: context,
      title: 'Delete Recipe',
      subtitle: 'Are you sure you want to delete this recipe?',
      confirmText: 'Delete',
      cancelText: 'Cancel',
    );
    if (confirmed != true) {
      print('Recipe deletion cancelled by user');
      return false; // User cancelled deletion
    }

    bool success = false;
    try {
      await callDataService(
        repo.deleteRecipe(cookbookId: cookbookId, recipeId: recipeId),
        onStart: () {
          print('Starting deletion for recipe ID: $recipeId in cookbook ID: $cookbookId');
          state = state.copyWith(status: AppStatus.loading);
        },
        onSuccess: (RecipeDeleteResponse response) async {
          print('API response: $response, success: ${response.success}');
          if (response.success == true) {
            print('Recipe deletion successful, updating data');
            state = state.copyWith(
              status: AppStatus.success,
              errorMessage: null,
            );
            if (context.mounted) {
              Utils().showFlushbar(
                context,
                message: 'Recipe deleted successfully',
                isError: false,
              );
            }
              updateData(context,  cookbookId: cookbookId);
            success = true;
          } else {
            print('API returned success: false, message: ${response.message}');
            state = state.copyWith(
              status: AppStatus.error,
              errorMessage: response.message ?? 'Failed to delete recipe',
            );
            if (context.mounted) {
              Utils().showFlushbar(context, message: state.errorMessage!, isError: true);
            }
            success = false;
          }
        },
        onError: (e) {
          print('Error during recipe deletion: $e');
          state = state.copyWith(
            status: AppStatus.error,
            errorMessage: e.toString() ?? 'Failed to delete recipe',
          );
          if (context.mounted) {
            Utils().showFlushbar(context, message: state.errorMessage!, isError: true);
          }
          success = false;
        },
        onComplete: () {
          print('Recipe deletion completed, success: $success');
        },
      );
    } catch (e) {
      print('Unexpected error in deleteRecipe: $e');
      state = state.copyWith(
        status: AppStatus.error,
        errorMessage: 'Unexpected error: $e',
      );
      if (context.mounted) {
        Utils().showFlushbar(context, message: state.errorMessage!, isError: true);
      }
      success = false;
    }

    print('Returning success: $success');
    return success;
  }

  void updateData(BuildContext context, {int? cookbookId}) {
    state = state.copyWith(status: AppStatus.loading);

    ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
          context: context,
          loadMore: false,
        );
    ref.read(recipeNotifierProvider.notifier).fetchRecipes(
          context: context,
          cookbookId: cookbookId ?? 0,
          reset: true,
          currentPage: 1,
        );
  }


  Future<void> showAddRecipeSuccessBottomSheet({
    required BuildContext context,
    required WidgetRef ref,
    bool callFromUpdate = false,
    VoidCallback? onSuccess,
  }) {
    var isApiCall = false;
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      isDismissible: false,
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.37,
          minChildSize: 0.3,
          maxChildSize: 0.4,
          expand: false,
          builder: (_, controller) {
            return AddRecipeSuccessBottomSheet(
              callFromUpdate: callFromUpdate,
              // onCreate: (name, context, ref) {
              //   isApiCall = true;
              // },
              onSuccess: onSuccess,
              scrollController: controller,
            );
          },
        );
      },
    ).whenComplete(() {
      // Reset state when bottom sheet is closed
      if (isApiCall) {
        ref
            .read(cookbookNotifierProvider.notifier)
            .fetchCookbooks(context: context);
      }
    });
  }
}

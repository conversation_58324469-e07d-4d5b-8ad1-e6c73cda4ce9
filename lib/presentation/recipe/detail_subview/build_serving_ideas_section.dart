import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/utils/device_utils.dart';

import '../../../app/imports/core_imports.dart';
import '../../../app/imports/packages_imports.dart';
import '../../../core/data/models/category_response.dart';
import '../../../core/data/models/cuisines_response.dart';
import '../../../core/data/models/recipe_detail_response.dart';
import '../../../core/data/models/recipe_response.dart';
import '../../../core/providers/single_recipe_notifier.dart';
import '../../../core/widgets/custom_doted_lines.dart';
import '../../../core/widgets/custom_info_cards.dart';
import '../../../core/widgets/custom_text.dart';
import '../../cookbook/widgets/custom_desc_text.dart';
import '../../cookbook/widgets/custom_title_text.dart';
import '../../cookbook/widgets/edit_button.dart';

Widget buildServingIdeasSection(
    String servingIdea,
    BuildContext context,
    List<Directions> directions,
    RecipeDetails recipeDetail,
    List<Categories>? categoriesList,
    List<Cuisines>? cuisinesList,
    int recipeId,
    int cookbookId,
    List<Recipe> recipesList,
    {required WidgetRef ref}) {
  return Container(
    padding: EdgeInsets.all(24.sp),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(16),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            CustomText(
              text: 'Serving Ideas',
              size: getDeviceType(context).name == 'mobile' ? 14 : 20,
              color: AppColors.primaryGreyColor,
              weight: FontWeight.w600,
            ),
            Spacer(),
            Visibility(
              visible: servingIdea.isNotEmpty,
              child: Align(
                alignment: Alignment.topRight,
                child: EditButton(onPressed: () {
                  ref
                      .read(singleRecipeNotifierProvider.notifier)
                      .goToEditRecipeScreen(
                          context,
                          recipeId,
                          cookbookId,
                          recipesList,
                          recipeDetail,
                          categoriesList ?? [],
                          cuisinesList ?? []);
                }),
              ),
            ),
          ],
        ),
        SizedBox(height: 20.h),
        CustomPaint(
          painter: DottedLinePainter(
            strokeWidth: 1,
            dashWidth: 6,
            color: AppColors.lightestGreyColor,
          ),
          size: Size(double.infinity, 2),
        ),
        SizedBox(height: 16.h),
        servingIdea.isNotEmpty
            ? CustomDescText(
                desc: servingIdea ?? '',
                size: 14,
                textColor: AppColors.primaryLightTextColor,
              )
            : CustomInfoCard(
                title: 'No serving suggestions added yet.',
                subheading: 'Add ideas on how to serve or present this dish.',
                actionText: 'Add Serving Ideas',
                onActionPressed: () {
                  ref
                      .read(singleRecipeNotifierProvider.notifier)
                      .goToEditRecipeScreen(
                          context,
                          recipeId,
                          cookbookId,
                          recipesList,
                          recipeDetail,
                          categoriesList!,
                          cuisinesList!);
                }),
      ],
    ),
  );
}

Widget buildWinePairingSection(
    String wine,
    BuildContext context,
    List<Directions> directions,
    RecipeDetails recipeDetail,
    List<Categories>? categoriesList,
    List<Cuisines>? cuisinesList,
    int recipeId,
    int cookbookId,
    List<Recipe> recipesList,
    {required WidgetRef ref}) {
  return Container(
    padding: EdgeInsets.all(24.sp),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(16),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            CustomText(
              text: 'Wine',
              size: getDeviceType(context).name == 'mobile' ? 14 : 20,
              color: AppColors.primaryGreyColor,
              weight: FontWeight.w600,
            ),
            Spacer(),
            Visibility(
              visible: wine.isNotEmpty,
              child: Align(
                alignment: Alignment.topRight,
                child: EditButton(onPressed: () {
                  ref
                      .read(singleRecipeNotifierProvider.notifier)
                      .goToEditRecipeScreen(
                          context,
                          recipeId,
                          cookbookId,
                          recipesList,
                          recipeDetail,
                          categoriesList ?? [],
                          cuisinesList ?? []);
                }),
              ),
            ),
          ],
        ),
        SizedBox(height: 20.h),
        CustomPaint(
          painter: DottedLinePainter(
            strokeWidth: 1,
            dashWidth: 6,
            color: AppColors.lightestGreyColor,
          ),
          size: Size(double.infinity, 2),
        ),
        SizedBox(height: 16.h),
        wine.isNotEmpty
            ? CustomDescText(
                desc: wine ?? '',
                size: 14,
                textColor: AppColors.primaryLightTextColor,
              )
            : CustomInfoCard(
                title: 'No wine pairing added',
                subheading: 'Suggest the perfect wine to complement this dish.',
                actionText: 'Add Wine Pairing',
                onActionPressed: () {
                  ref
                      .read(singleRecipeNotifierProvider.notifier)
                      .goToEditRecipeScreen(
                          context,
                          recipeId,
                          cookbookId,
                          recipesList,
                          recipeDetail,
                          categoriesList!,
                          cuisinesList!);
                }),
        SizedBox(height: 20.h),
      ],
    ),
  );
}

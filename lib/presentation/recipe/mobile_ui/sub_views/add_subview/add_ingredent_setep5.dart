import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/assets_manager.dart';
import 'package:mastercookai/core/widgets/custom_ccon_text_button.dart';
import '../../../../../app/theme/colors.dart';
import '../../../../../core/providers/recipe/ingrident_provider.dart';
import '../../../../../core/providers/recipe/step_Provider.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../../core/widgets/custom_text.dart';
import '../../../subview/custom_ingredent_textfield.dart';

class AddIngredentSetep5 extends ConsumerWidget {
  final bool isCallFromEdit;
  final VoidCallback? onUpdateData;

  AddIngredentSetep5({
    super.key,
    this.isCallFromEdit = false,
    this.onUpdateData,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final ingredientsNotifier = ref.watch(ingredientsProvider.notifier);
    final controllers = ingredientsNotifier.controllers;

    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CustomText(
                    text: 'Ingredients',
                    size: 14,
                    weight: FontWeight.w500,
                    color: AppColors.primaryGreyColor),
                Spacer(),
                CustomIconTextButton(
                  iconPath: AssetsManager.ic_plus,
                  text: 'Add row',
                  iconColor: Colors.white,
                  backgroundColor: Colors.blue,
                  textColor: Colors.white,
                  height: 40,
                  onTap: () {
                    ingredientsNotifier.addController();
                  },
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Expanded(
              child: ListView.builder(
                shrinkWrap: true,
                // physics: const NeverScrollableScrollPhysics(),
                itemCount: controllers.length,
                itemBuilder: (context, index) {
                  return Padding(
                    padding: EdgeInsets.symmetric(vertical: 8.h),
                    child: IngredientTextField(
                      controller: controllers[index],
                      hintText: 'Add ingredient',
                      //autoFocus: true,
                      height: 70.h,
                      onChanged: (value) {
                        // Update provider on text change
                        ingredientsNotifier.setIngredients();
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: CustomButton(
            borderRadius: 10,
            text: 'Next',
            onPressed: () {
              ref.read(stepProvider.notifier).nextStep();
            },
          ),
        ),
      ),
    );
  }
}

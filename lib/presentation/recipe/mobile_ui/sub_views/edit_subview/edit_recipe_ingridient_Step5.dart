import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/data/models/recipe_detail_response.dart';
import 'package:mastercookai/core/providers/recipe/step_Provider.dart';
import '../../../../../app/imports/core_imports.dart';
import '../../../../../app/theme/colors.dart';
import '../../../../../core/providers/recipe/ingrident_provider.dart';
import '../../../../../core/providers/recipe/media_provider.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../../core/widgets/custom_ccon_text_button.dart';
import '../../../../../core/widgets/custom_text.dart';
import '../../../edit_recipe_subview/buildIngredientsSection.dart';
import '../../../subview/add_ingredent.dart';
import '../../../subview/custom_notes_widget.dart';
import '../../../subview/media_picker_grid.dart';

Widget editRecipeIngridientStep5(
    {required VoidCallback? onUpdateData,
    required WidgetRef ref,
    required BuildContext context}) {
  return Scaffold(
    body: Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CustomText(
                  text: 'Update Ingredients',
                  size: 14,
                  weight: FontWeight.w500,
                  color: AppColors.primaryGreyColor),
              Spacer(),
              CustomIconTextButton(
                iconPath: AssetsManager.ic_plus,
                text: 'Add row',
                iconColor: Colors.white,
                backgroundColor: Colors.blue,
                textColor: Colors.white,
                height: 40,
                onTap: () {
                  ref.watch(ingredientsProvider.notifier).addController();
                },
              ),
            ],
          ),
          SizedBox(
            height: 12,
          ),
          AddIngredientScreen(
            isCallFromEdit: false,
            onUpdateData: () {},
            isCallFromMobile: true,
          )
        ],
      ),
    ),
    bottomNavigationBar: Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 35),
      child: Row(
        children: [
          Expanded(
            flex: 1,
            child: CustomButton(
                borderRadius: 10,
                text: 'Next',
                onPressed: () {
                  ref.read(stepProvider.notifier).nextStep();
                }),
          ),
          SizedBox(
            width: 16,
          ),
          Expanded(
              flex: 1,
              child: CustomButton(
                  borderRadius: 10, text: 'Save', onPressed: onUpdateData!)),
        ],
      ),
    ),
  );
}

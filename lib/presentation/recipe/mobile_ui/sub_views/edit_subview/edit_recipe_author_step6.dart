import 'dart:io';
import 'package:mastercookai/app/assets_manager.dart';
import '../../../../../app/imports/packages_imports.dart';
import '../../../../../app/theme/colors.dart';
import '../../../../../core/helpers/media_picker_service.dart';
import '../../../../../core/providers/recipe/author_provider.dart';
import '../../../../../core/providers/recipe/step_Provider.dart';
import '../../../../../core/widgets/circular_image.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../../core/widgets/custom_text.dart';
import '../../../../../core/widgets/image_cropper/image_cropper.dart';
import '../../../subview/custom_ingredent_textfield.dart';

class EditRecipeAuthorStep6 extends ConsumerStatefulWidget {
  String? author;
  String? authorMediaUrl;
  String? copyright;
  String? source;
  bool? isCallFromEdit;
  VoidCallback? onUpdateData;

  EditRecipeAuthorStep6({
    super.key,
    this.author,
    this.authorMediaUrl,
    this.copyright,
    this.source,
    this.isCallFromEdit = false,
    this.onUpdateData,
  });

  @override
  ConsumerState<EditRecipeAuthorStep6> createState() =>
      _CustomAuthorWidgetState();
}

class _CustomAuthorWidgetState extends ConsumerState<EditRecipeAuthorStep6> {
  late TextEditingController sourceController;
  late TextEditingController copyrightController;
  late TextEditingController authorNameController;

  File? authorImage;

  @override
  void initState() {
    super.initState();
    // Initialize noteController with value from notesProvider or widget.notes
    authorNameController = TextEditingController(
      text: widget.author ?? '',
    );
    sourceController = TextEditingController(
      text: widget.source ?? '',
    );
    copyrightController = TextEditingController(
      text: widget.copyright ?? '',
    );
  }

  void _pickAuthorImage() async {
    final file = await MediaPickerService.pickSingleImage();
    if (file != null && mounted) {
      final croppedImageFile = await Navigator.of(context).push<File?>(
        MaterialPageRoute(
          builder: (context) => ImageCropper(
            pickedImage: file,
            showCropPresets: true,
            showGridLines: false,
            enableFreeformCrop: true,
          ),
        ),
      );

      if (croppedImageFile != null && mounted) {
        setState(() {
          authorImage = croppedImageFile;
          ref.read(authorProvider.notifier).updateImage(croppedImageFile);
        });
      } else {
        print("Cropped image is null or user canceled.");
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authorState = ref.watch(authorProvider);
    print("Build: authorState.image = ${authorState.image?.path}");

    // Reset local authorImage when provider state is reset
    if (authorState.image == null && authorImage != null) {
      authorImage = null;
    }

    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomText(
              text: 'Update Author Info',
              size: 14,
              weight: FontWeight.w500,
              color: AppColors.primaryGreyColor,
            ),
            SizedBox(
              height: 10,
            ),

            Center(
              child: CircularImage(
                imageFile: authorImage,
                // Get cropped image from authorProvider
                imageUrl: widget.authorMediaUrl,
                // Fallback to network URL if provided
                placeholderAsset: AssetsManager.profile_placeholder,
                // Replace with your asset path
                radius: 350.0,
                // Matches your original radius
                onTap: () => _pickAuthorImage(),
                borderColor: Colors.grey.shade300,
                placeholderText: 'Add Image',
              ),
            ),
            SizedBox(
              height: 16,
            ),

            IngredientTextField(
              hintText: "Author",
              controller: authorNameController,
              height: 70.h,
              onChanged: (value) =>
                  ref.read(authorProvider.notifier).updateAuthorName(value),
            ),
            SizedBox(height: 8.h),
            IngredientTextField(
              hintText: "Source",
              controller: sourceController,
              height: 70.h,
              onChanged: (value) =>
                  ref.read(authorProvider.notifier).updateSource(value),
            ),
            SizedBox(height: 8.h),
            IngredientTextField(
              hintText: "Copyright",
              controller: copyrightController,
              height: 70.h,
              onChanged: (value) =>
                  ref.read(authorProvider.notifier).updateCopyright(value),
            ),

            SizedBox(height: 12),
            // CustomButton(text: "Save changes", onPressed: (){} , width: 230.w, fontSize: 20.sp,),
            // Visibility(
            //   visible: widget.isCallFromEdit ?? false,
            //   child: Padding(
            //     padding: const EdgeInsets.symmetric(horizontal: 50.0),
            //     child: CustomButton(
            //       text: "Save Changes",
            //       fontSize: 14,
            //       onPressed: () {
            //         // Ensure ingredients are updated before notifying parent
            //         widget.onUpdateData?.call();
            //       },
            //     ),
            //   ),
            //  ),
          ],
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 35),
        child: Row(
          children: [
            Expanded(
              flex: 1,
              child: CustomButton(
                  borderRadius: 10,
                  text: 'Next',
                  onPressed: () {
                    ref.read(stepProvider.notifier).nextStep();
                  }),
            ),
            SizedBox(
              width: 16,
            ),
            Expanded(
                flex: 1,
                child: CustomButton(
                    borderRadius: 10,
                    text: 'Save',
                    onPressed: () {
                      widget.onUpdateData?.call();
                    })),
          ],
        ),
      ),
    );
  }
}
